<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tab Screen Share - Web Client</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #1a1a1a;
        color: #ffffff;
        overflow: hidden;
      }

      .container {
        display: flex;
        flex-direction: column;
        height: 100vh;
      }

      .header {
        background: #2d2d2d;
        padding: 15px 20px;
        border-bottom: 1px solid #444;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .title {
        font-size: 20px;
        font-weight: 600;
      }

      .status {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .status-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #ff4757;
      }

      .status-dot.connected {
        background: #2ed573;
      }

      .status-dot.streaming {
        background: #ffa502;
        animation: pulse 1s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      .controls {
        background: #2d2d2d;
        padding: 10px 20px;
        border-bottom: 1px solid #444;
        display: flex;
        gap: 10px;
        align-items: center;
      }

      .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background 0.2s;
      }

      .btn-primary {
        background: #4caf50;
        color: white;
      }

      .btn-primary:hover {
        background: #45a049;
      }

      .btn-secondary {
        background: #6c757d;
        color: white;
      }

      .btn-secondary:hover {
        background: #5a6268;
      }

      .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .room-input {
        padding: 8px 12px;
        border: 1px solid #555;
        border-radius: 4px;
        background: #3d3d3d;
        color: white;
        font-size: 14px;
      }

      .main-content {
        flex: 1;
        display: flex;
        position: relative;
        background: #000;
      }

      .video-container {
        flex: 1;
        position: relative;
        overflow: auto;
        padding: 10px;
      }

      .streams-grid {
        display: grid;
        gap: 10px;
        height: 100%;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        grid-auto-rows: minmax(200px, 1fr);
      }

      .stream-item {
        position: relative;
        background: #2d2d2d;
        border-radius: 8px;
        overflow: hidden;
        border: 2px solid #444;
        transition: border-color 0.3s;
      }

      .stream-item:hover {
        border-color: #4caf50;
      }

      .stream-video {
        width: 100%;
        height: 100%;
        object-fit: contain;
        background: #000;
      }

      .stream-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        color: white;
        padding: 10px;
        font-size: 12px;
      }

      .stream-title {
        font-weight: bold;
        margin-bottom: 4px;
      }

      .stream-info {
        opacity: 0.8;
      }

      .stream-controls {
        position: absolute;
        top: 8px;
        right: 8px;
        display: flex;
        gap: 4px;
      }

      .stream-btn {
        background: rgba(0, 0, 0, 0.7);
        border: none;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 11px;
      }

      .stream-btn:hover {
        background: rgba(0, 0, 0, 0.9);
      }

      .no-stream {
        text-align: center;
        color: #888;
        grid-column: 1 / -1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
      }

      .no-stream h2 {
        font-size: 24px;
        margin-bottom: 10px;
      }

      .sidebar {
        width: 300px;
        background: #2d2d2d;
        border-left: 1px solid #444;
        display: flex;
        flex-direction: column;
      }

      .sidebar-section {
        padding: 15px;
        border-bottom: 1px solid #444;
      }

      .sidebar-section h3 {
        font-size: 16px;
        margin-bottom: 10px;
        color: #fff;
      }

      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .info-label {
        color: #aaa;
      }

      .info-value {
        color: #fff;
        font-weight: 500;
      }

      .log-container {
        flex: 1;
        background: #1a1a1a;
        padding: 15px;
        overflow-y: auto;
        font-family: "Courier New", monospace;
        font-size: 12px;
      }

      .log-entry {
        margin-bottom: 5px;
        padding: 2px 0;
      }

      .log-entry.info {
        color: #17a2b8;
      }

      .log-entry.success {
        color: #28a745;
      }

      .log-entry.warning {
        color: #ffc107;
      }

      .log-entry.error {
        color: #dc3545;
      }

      .fullscreen-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
      }

      .fullscreen-btn:hover {
        background: rgba(0, 0, 0, 0.9);
      }
    </style>
  </head>
  <body>
    <div class="container">
      <header class="header">
        <div class="title">Tab Screen Share - Web Client</div>
        <div class="status">
          <div class="status-dot" id="statusDot"></div>
          <span id="statusText">Disconnected</span>
        </div>
      </header>

      <div class="controls">
        <input
          type="text"
          id="roomInput"
          class="room-input"
          placeholder="Room ID (default: default-room)"
          value="default-room"
        />
        <button id="connectBtn" class="btn btn-primary">Connect</button>
        <button id="disconnectBtn" class="btn btn-secondary" disabled>
          Disconnect
        </button>
        <span style="margin-left: auto; font-size: 14px; color: #aaa">
          WebSocket: <span id="wsStatus">Disconnected</span>
        </span>
      </div>

      <div class="main-content">
        <div class="video-container">
          <div class="streams-grid" id="streamsGrid">
            <div class="no-stream" id="noStreamMessage">
              <h2>No Streams Available</h2>
              <p>
                Connect to a room and wait for Chrome extensions to start
                streaming
              </p>
            </div>
          </div>
        </div>

        <div class="sidebar">
          <div class="sidebar-section">
            <h3>Connection Info</h3>
            <div class="info-item">
              <span class="info-label">Client ID:</span>
              <span class="info-value" id="clientId">-</span>
            </div>
            <div class="info-item">
              <span class="info-label">Room:</span>
              <span class="info-value" id="currentRoom">-</span>
            </div>
            <div class="info-item">
              <span class="info-label">Peer State:</span>
              <span class="info-value" id="peerState">-</span>
            </div>
          </div>

          <div class="sidebar-section">
            <h3>Streams Info</h3>
            <div class="info-item">
              <span class="info-label">Active Streams:</span>
              <span class="info-value" id="activeStreamsCount">0</span>
            </div>
            <div class="info-item">
              <span class="info-label">Total Video Tracks:</span>
              <span class="info-value" id="totalVideoTracks">0</span>
            </div>
            <div class="info-item">
              <span class="info-label">Total Audio Tracks:</span>
              <span class="info-value" id="totalAudioTracks">0</span>
            </div>
          </div>

          <div class="sidebar-section">
            <h3>Stream List</h3>
            <div id="streamsList" style="max-height: 200px; overflow-y: auto">
              <p style="color: #888; font-size: 12px">No active streams</p>
            </div>
          </div>

          <div class="log-container" id="logContainer">
            <div class="log-entry info">Web client initialized</div>
          </div>
        </div>
      </div>
    </div>

    <script src="client.js"></script>
  </body>
</html>
