// Offscreen document for handling getUserMedia in Chrome MV3
// This runs in a separate context that can access media APIs

class OffscreenMediaHandler {
  constructor() {
    this.activeStreams = new Map();
    this.peerConnections = new Map();
    this.signalingChannel = null;
    this.clientId = null;
    this.isSignalingConnected = false;
    this.setupMessageListener();
    this.setupWebRTC();
    console.log("Offscreen media handler initialized");
  }

  setupWebRTC() {
    // WebRTC configuration
    this.rtcConfiguration = {
      iceServers: [
        { urls: "stun:stun.l.google.com:19302" },
        { urls: "stun:stun1.l.google.com:19302" },
      ],
    };
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async response
    });
  }

  async handleMessage(message, sender, sendResponse) {
    console.log("offscreen message", { message });
    try {
      switch (message.action) {
        case "startTabCapture":
          const stream = await this.startTabCapture(
            message.streamId,
            message.uniqueStreamId,
            message.tabId
          );
          sendResponse({
            success: true,
            data: {
              streamId: message.streamId,
              uniqueStreamId: message.uniqueStreamId,
            },
          });
          break;

        case "stopTabCapture":
          await this.stopTabCapture(message.streamId);
          sendResponse({ success: true });
          break;

        case "getStreamInfo":
          const info = this.getStreamInfo(message.streamId);
          sendResponse({ success: true, data: info });
          break;

        case "getStream":
          this.getStream(message.streamId, sendResponse);
          return true; // Keep message channel open for async response

        case "setupSignaling":
          this.setupSignalingChannel(message.url);
          sendResponse({ success: true });
          break;

        case "createPeerConnection":
          await this.createPeerConnection(
            message.uniqueStreamId,
            message.streamId,
            sendResponse
          );
          return true;

        case "handleSignalingMessage":
          this.handleSignalingMessage(message.data);
          sendResponse({ success: true });
          break;

        case "cleanup":
          this.cleanup();
          sendResponse({ success: true });
          break;

        default:
          sendResponse({
            success: false,
            error: `Unknown action ${message.action}`,
          });
      }
    } catch (error) {
      console.error("Offscreen handler error:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async startTabCapture(streamId, uniqueStreamId, tabId) {
    try {
      console.log(
        "Starting tab capture with stream ID:",
        streamId,
        "unique ID:",
        uniqueStreamId,
        "for tab:",
        tabId
      );

      if (!streamId) {
        throw new Error("No stream ID provided");
      }

      // Check if getUserMedia is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("getUserMedia is not available in this context");
      }

      // Use getUserMedia with the stream ID from tabCapture API
      let mediaStream;
      try {
        mediaStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            mandatory: {
              chromeMediaSource: "tab",
              chromeMediaSourceId: streamId,
            },
          },
          video: {
            mandatory: {
              chromeMediaSource: "tab",
              chromeMediaSourceId: streamId,
            },
          },
        });
      } catch (getUserMediaError) {
        console.error("getUserMedia failed:", getUserMediaError);

        // Provide more specific error messages
        if (getUserMediaError.name === "NotAllowedError") {
          throw new Error(
            "Permission denied for tab capture. Please ensure the extension has the necessary permissions."
          );
        } else if (getUserMediaError.name === "NotFoundError") {
          throw new Error(
            "No media tracks found. The tab may not have any audio or video content."
          );
        } else if (getUserMediaError.name === "NotSupportedError") {
          throw new Error(
            "Tab capture is not supported in this browser or context."
          );
        } else if (getUserMediaError.name === "InvalidStateError") {
          throw new Error("Invalid stream ID or tab state. Please try again.");
        } else {
          throw new Error(`Media capture failed: ${getUserMediaError.message}`);
        }
      }

      if (!mediaStream) {
        throw new Error("Failed to obtain media stream");
      }

      // Validate that we got tracks
      const audioTracks = mediaStream.getAudioTracks().length;
      const videoTracks = mediaStream.getVideoTracks().length;

      if (audioTracks === 0 && videoTracks === 0) {
        mediaStream.getTracks().forEach((track) => track.stop());
        throw new Error("No audio or video tracks available from the tab");
      }

      // Store the stream for later management using unique ID
      this.activeStreams.set(uniqueStreamId, {
        stream: mediaStream,
        chromeStreamId: streamId, // Keep Chrome's stream ID for reference
        startTime: Date.now(),
        audioTracks: audioTracks,
        videoTracks: videoTracks,
        tabId: tabId,
      });

      console.log("Tab capture started successfully:", {
        streamId,
        uniqueStreamId,
        audioTracks: audioTracks,
        videoTracks: videoTracks,
      });

      // Send stream info back to background script
      try {
        chrome.runtime.sendMessage({
          action: "streamReady",
          streamId: streamId,
          uniqueStreamId: uniqueStreamId,
          audioTracks: audioTracks,
          videoTracks: videoTracks,
        });
      } catch (messageError) {
        console.warn("Failed to send stream ready message:", messageError);
        // Don't fail the capture if messaging fails
      }

      return mediaStream;
    } catch (error) {
      console.error("Failed to start tab capture:", error);

      // Clean up any partial state
      if (this.activeStreams.has(streamId)) {
        const streamInfo = this.activeStreams.get(streamId);
        if (streamInfo.stream) {
          streamInfo.stream.getTracks().forEach((track) => track.stop());
        }
        this.activeStreams.delete(streamId);
      }

      throw error;
    }
  }

  async stopTabCapture(streamId) {
    const streamInfo = this.activeStreams.get(streamId);
    if (streamInfo) {
      // Stop all tracks
      streamInfo.stream.getTracks().forEach((track) => {
        track.stop();
      });

      this.activeStreams.delete(streamId);
      console.log("Tab capture stopped for stream:", streamId);

      // Notify background script
      chrome.runtime.sendMessage({
        action: "streamStopped",
        streamId: streamId,
      });
    }
  }

  getStreamInfo(streamId) {
    const streamInfo = this.activeStreams.get(streamId);
    if (!streamInfo) {
      return { isActive: false };
    }

    return {
      isActive: true,
      startTime: streamInfo.startTime,
      duration: Date.now() - streamInfo.startTime,
      audioTracks: streamInfo.audioTracks,
      videoTracks: streamInfo.videoTracks,
    };
  }

  // Get the actual stream object for WebRTC
  async getStream(streamId, sendResponse) {
    try {
      const streamInfo = this.activeStreams.get(streamId);
      if (!streamInfo || !streamInfo.stream) {
        sendResponse({ success: false, error: "Stream not found" });
        return;
      }

      // Note: We can't directly transfer MediaStream objects between contexts
      // Instead, we'll need to use a different approach for WebRTC integration
      // For now, we'll return stream information
      sendResponse({
        success: true,
        data: {
          streamId: streamId,
          hasStream: true,
          videoTracks: streamInfo.videoTracks,
          audioTracks: streamInfo.audioTracks,
        },
      });
    } catch (error) {
      console.error("Error getting stream:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  // WebRTC Methods
  setupSignalingChannel(url = "ws://localhost:3001") {
    try {
      console.log("Setting up signaling channel:", url);
      this.signalingChannel = new WebSocket(url);

      this.signalingChannel.onopen = () => {
        console.log("Signaling channel connected");
        this.isSignalingConnected = true;

        // Register as extension
        this.sendSignalingMessage({
          type: "register",
          clientType: "extension",
        });
      };

      this.signalingChannel.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleSignalingMessage(message);
        } catch (error) {
          console.error("Invalid signaling message:", error);
        }
      };

      this.signalingChannel.onerror = (error) => {
        console.error("Signaling channel error:", error);
        this.isSignalingConnected = false;
      };

      this.signalingChannel.onclose = () => {
        console.log("Signaling channel closed");
        this.isSignalingConnected = false;
      };
    } catch (error) {
      console.error("Failed to setup signaling channel:", error);
    }
  }

  sendSignalingMessage(message) {
    if (
      this.signalingChannel &&
      this.signalingChannel.readyState === WebSocket.OPEN
    ) {
      this.signalingChannel.send(JSON.stringify(message));
      console.log("Signaling message sent:", message.type);
    } else {
      console.warn(
        "No signaling channel available. Message not sent:",
        message
      );
    }
  }

  async createPeerConnection(uniqueStreamId, chromeStreamId, sendResponse) {
    try {
      console.log(
        "Creating peer connection for unique stream:",
        uniqueStreamId,
        "chrome stream:",
        chromeStreamId
      );

      const streamInfo = this.activeStreams.get(uniqueStreamId);
      if (!streamInfo || !streamInfo.stream) {
        sendResponse({ success: false, error: "Stream not found" });
        return;
      }

      const pc = new RTCPeerConnection(this.rtcConfiguration);

      // Set up event handlers
      pc.onicecandidate = (event) => {
        if (event.candidate) {
          this.sendSignalingMessage({
            type: "ice-candidate",
            candidate: event.candidate,
            targetClientId: this.currentClientId,
            streamId: uniqueStreamId,
          });
        }
      };

      pc.onconnectionstatechange = () => {
        console.log("Connection state:", pc.connectionState);
        if (pc.connectionState === "connected") {
          console.log("WebRTC connection established");
        }
      };

      // Add stream tracks to peer connection
      streamInfo.stream.getTracks().forEach((track) => {
        pc.addTrack(track, streamInfo.stream);
        console.log(`Added ${track.kind} track to peer connection`);
      });

      this.peerConnections.set(uniqueStreamId, pc);

      // Join room and notify stream availability
      this.sendSignalingMessage({
        type: "join-room",
        roomId: "default-room",
      });

      // Notify about stream availability
      const startStreamMessage = {
        type: "start-stream",
        streamId: uniqueStreamId,
        tabId: streamInfo.tabId || null,
        streamInfo: {
          videoTracks: streamInfo.videoTracks,
          audioTracks: streamInfo.audioTracks,
          tabId: streamInfo.tabId || null,
        },
      };
      console.log("Sending start-stream message:", startStreamMessage);
      this.sendSignalingMessage(startStreamMessage);

      sendResponse({ success: true, data: { streamId: uniqueStreamId } });
    } catch (error) {
      console.error("Failed to create peer connection:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  handleSignalingMessage(message) {
    console.log("Received signaling message:", message.type);

    switch (message.type) {
      case "welcome":
        this.clientId = message.clientId;
        console.log("Assigned client ID:", this.clientId);
        break;

      case "registered":
        console.log("Registered as extension");
        break;

      case "room-joined":
        console.log("Joined room:", message.roomId);
        break;

      case "client-joined":
        if (message.clientType === "client") {
          console.log("Web client joined room:", message.clientId);
          this.currentClientId = message.clientId;
          this.createOfferForClient(message.clientId);
        }
        break;

      case "answer":
        this.handleAnswer(message.answer, message.streamId);
        break;

      case "ice-candidate":
        this.handleIceCandidate(message.candidate, message.streamId);
        break;

      default:
        console.warn("Unknown signaling message type:", message.type);
    }
  }

  async createOfferForClient(clientId) {
    try {
      console.log("Creating offer for client:", clientId);
      console.log(
        "Available peer connections:",
        Array.from(this.peerConnections.keys())
      );

      // Find an active peer connection
      for (const [streamId, pc] of this.peerConnections) {
        console.log(
          `Checking stream ${streamId}, connection state: ${pc.connectionState}`
        );
        if (pc.connectionState !== "closed") {
          const offer = await pc.createOffer();
          await pc.setLocalDescription(offer);

          const offerMessage = {
            type: "offer",
            offer: offer,
            targetClientId: clientId,
            streamId: streamId,
          };
          console.log("Sending offer message:", offerMessage);
          this.sendSignalingMessage(offerMessage);

          console.log(
            `Sent offer to client ${clientId} for stream ${streamId}`
          );
          break;
        }
      }

      if (this.peerConnections.size === 0) {
        console.warn("No peer connections available to create offer");
      }
    } catch (error) {
      console.error("Failed to create offer:", error);
    }
  }

  async handleAnswer(answer, streamId) {
    try {
      const pc = this.peerConnections.get(streamId);
      if (!pc) {
        console.warn(`No peer connection found for stream ${streamId}`);
        return;
      }

      if (pc.signalingState === "have-local-offer") {
        await pc.setRemoteDescription(answer);
        console.log(`Applied answer from client for stream ${streamId}`);
      } else {
        console.warn(
          `Peer connection for stream ${streamId} not in correct state: ${pc.signalingState}`
        );
      }
    } catch (error) {
      console.error(`Failed to handle answer for stream ${streamId}:`, error);
    }
  }

  async handleIceCandidate(candidate, streamId) {
    try {
      const pc = this.peerConnections.get(streamId);
      if (!pc) {
        console.warn(`No peer connection found for stream ${streamId}`);
        return;
      }

      if (pc.remoteDescription) {
        await pc.addIceCandidate(candidate);
        console.log(`Added ICE candidate for stream ${streamId}`);
      } else {
        console.warn(
          `Peer connection for stream ${streamId} has no remote description yet`
        );
      }
    } catch (error) {
      console.error(
        `Failed to add ICE candidate for stream ${streamId}:`,
        error
      );
    }
  }

  // Clean up all streams and connections
  cleanup() {
    // Close peer connections
    for (const [streamId, pc] of this.peerConnections) {
      pc.close();
    }
    this.peerConnections.clear();

    // Stop streams
    for (const [streamId, streamInfo] of this.activeStreams) {
      streamInfo.stream.getTracks().forEach((track) => track.stop());
    }
    this.activeStreams.clear();

    // Close signaling channel
    if (this.signalingChannel) {
      this.signalingChannel.close();
    }

    console.log("All streams and connections cleaned up");
  }
}

// Initialize the offscreen media handler
const offscreenHandler = new OffscreenMediaHandler();

// Clean up on page unload
window.addEventListener("beforeunload", () => {
  offscreenHandler.cleanup();
});
