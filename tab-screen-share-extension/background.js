// Background service worker for Tab Screen Share Extension

class TabScreenShareExtension {
  constructor() {
    this.activeCaptures = new Map(); // tabId -> captureInfo
    this.activeStreams = new Map(); // streamId -> { tabId, captureInfo, webrtcInfo }
    this.offscreenDocumentCreated = false;
    this.setupEventListeners();

    // Initialize WebRTC signaling
    this.initializeWebRTC();
  }

  // Generate unique stream ID
  generateStreamId() {
    return `stream_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`;
  }

  async initializeWebRTC() {
    try {
      // Setup signaling channel in offscreen document
      await this.ensureOffscreenDocument();
      await chrome.runtime.sendMessage({
        action: "setupSignaling",
        url: "ws://localhost:3001",
      });
      console.log("WebRTC signaling initialized");
    } catch (error) {
      console.error("Failed to initialize WebRTC:", error);
    }
  }

  setupEventListeners() {
    // Handle extension installation
    chrome.runtime.onInstalled.addListener(() => {
      console.log("Tab Screen Share Extension installed");
    });

    // Handle messages from popup and offscreen document
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Handle tab removal - cleanup any active captures
    chrome.tabs.onRemoved.addListener((tabId) => {
      this.stopCapture(tabId);
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case "getActiveTab":
          const activeTab = await this.getActiveTab();
          sendResponse({ success: true, data: activeTab });
          break;

        case "startCapture":
          // Ensure we're working with the active tab
          const currentTab = await this.getActiveTab();
          if (!currentTab) {
            throw new Error("No active tab found");
          }

          // Check if tab is capturable (not chrome:// pages, etc.)
          if (this.isSystemPage(currentTab.url)) {
            throw new Error(
              "Cannot capture system pages. Please navigate to a regular webpage."
            );
          }

          // Check if tab is still valid
          if (!currentTab.id || currentTab.id === chrome.tabs.TAB_ID_NONE) {
            throw new Error("Invalid tab ID. Please try again.");
          }

          console.log(
            "Starting capture for tab:",
            currentTab.id,
            currentTab.url
          );
          const captureResult = await this.startTabCapture(currentTab.id);
          sendResponse({ success: true, data: captureResult });
          break;

        case "stopCapture":
          await this.stopCapture(message.tabId);
          sendResponse({ success: true });
          break;

        case "getCaptureStatus":
          const status = this.getCaptureStatus(message.tabId);
          console.log("getCaptureStatus for tab", message.tabId, ":", status);
          sendResponse({ success: true, data: status });
          break;

        // WebRTC related actions
        case "startWebRTCStream":
          const webrtcResult = await this.startWebRTCStream(message.tabId);
          sendResponse({ success: true, data: webrtcResult });
          break;

        case "stopWebRTCStream":
          await this.stopWebRTCStream(message.tabId);
          sendResponse({ success: true });
          break;

        // Handle messages from offscreen document
        case "streamReady":
          console.log("Received streamReady message:", message);
          this.handleStreamReady(message);
          break;

        case "streamStopped":
          console.log("Received streamStopped message:", message);
          this.handleStreamStopped(message);
          break;

        default:
          sendResponse({
            success: false,
            error: `Unknown action:: ${message}`,
          });
      }
    } catch (error) {
      console.error("Background script error:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async getActiveTab() {
    try {
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });

      if (!tab) {
        throw new Error("No active tab found");
      }

      return tab;
    } catch (error) {
      console.error("Failed to get active tab:", error);
      throw new Error("Unable to access current tab. Please try again.");
    }
  }

  isSystemPage(url) {
    if (!url) return true;

    const systemPrefixes = [
      "chrome://",
      "chrome-extension://",
      "edge://",
      "about:",
      "moz-extension://",
      "safari-extension://",
      "data:",
      "file://",
    ];

    return systemPrefixes.some((prefix) => url.startsWith(prefix));
  }

  async ensureOffscreenDocument() {
    if (this.offscreenDocumentCreated) {
      return;
    }

    try {
      await chrome.offscreen.createDocument({
        url: "offscreen.html",
        reasons: ["USER_MEDIA"],
        justification: "Required to access getUserMedia for tab capture",
      });
      this.offscreenDocumentCreated = true;
      console.log("Offscreen document created");
    } catch (error) {
      if (error.message.includes("Only a single offscreen document")) {
        this.offscreenDocumentCreated = true;
        console.log("Offscreen document already exists");
      } else {
        throw error;
      }
    }
  }

  async handleStreamReady(message) {
    // Find capture by streamId since the message comes with streamId
    let foundCapture = null;
    let foundTabId = null;

    for (const [tabId, capture] of this.activeCaptures) {
      if (capture.streamId === message.streamId) {
        foundCapture = capture;
        foundTabId = tabId;
        break;
      }
    }

    if (foundCapture) {
      foundCapture.audioTracks = message.audioTracks;
      foundCapture.videoTracks = message.videoTracks;
      foundCapture.isActive = true;
      console.log(
        "Stream ready for tab:",
        foundTabId,
        "streamId:",
        message.streamId
      );

      // Start WebRTC streaming
      try {
        await this.startWebRTCStream(foundTabId);
        console.log("WebRTC streaming started for tab:", foundTabId);
      } catch (error) {
        console.error("Failed to start WebRTC streaming:", error);
      }
    } else {
      console.warn("No capture found for streamId:", message.streamId);
    }
  }

  handleStreamStopped(message) {
    // Find and remove capture by streamId
    for (const [tabId, capture] of this.activeCaptures) {
      if (capture.streamId === message.streamId) {
        this.activeCaptures.delete(tabId);
        console.log(
          "Stream stopped for tab:",
          tabId,
          "streamId:",
          message.streamId
        );
        break;
      }
    }
  }

  async startTabCapture(tabId) {
    try {
      // Check if already capturing this tab
      if (this.activeCaptures.has(tabId)) {
        throw new Error("Tab is already being captured");
      }

      console.log("Attempting to capture tab:", tabId);

      // Validate tab still exists
      try {
        const tab = await chrome.tabs.get(tabId);
        if (!tab) {
          throw new Error("Tab no longer exists");
        }
      } catch (tabError) {
        throw new Error("Tab is no longer available. It may have been closed.");
      }

      // Ensure offscreen document exists
      await this.ensureOffscreenDocument();

      // Check if tabCapture API is available
      if (!chrome.tabCapture) {
        throw new Error(
          "Tab capture API is not available. Please check extension permissions."
        );
      }

      // Check if getMediaStreamId is available (Chrome 116+)
      if (!chrome.tabCapture.getMediaStreamId) {
        throw new Error(
          "Tab capture API is not supported in this Chrome version. Please update to Chrome 116 or later."
        );
      }

      // Get media stream ID for the tab
      let streamId;
      try {
        streamId = await chrome.tabCapture.getMediaStreamId({
          targetTabId: tabId,
        });
      } catch (streamError) {
        console.error("Failed to get stream ID:", streamError);
        throw new Error(
          "Failed to get permission for tab capture. Please ensure the tab is not playing protected content."
        );
      }

      if (!streamId) {
        throw new Error(
          "Unable to capture this tab. It may contain protected content or be in an unsupported state."
        );
      }

      console.log("Got stream ID:", streamId);

      // Generate unique stream ID for tracking
      const uniqueStreamId = this.generateStreamId();

      // Store capture info
      const captureInfo = {
        tabId: tabId,
        streamId: streamId, // Chrome's stream ID
        uniqueStreamId: uniqueStreamId, // Our unique ID
        startTime: Date.now(),
        isActive: false,
        audioTracks: 0,
        videoTracks: 0,
        method: "tabCapture",
      };

      this.activeCaptures.set(tabId, captureInfo);
      this.activeStreams.set(uniqueStreamId, {
        tabId: tabId,
        captureInfo: captureInfo,
        webrtcInfo: null,
      });

      console.log("Stored capture info for tab", tabId, ":", captureInfo);
      console.log(
        "Active captures:",
        Array.from(this.activeCaptures.entries())
      );
      console.log("Active streams:", Array.from(this.activeStreams.entries()));

      // Send message to offscreen document to start capture
      try {
        await chrome.runtime.sendMessage({
          action: "startTabCapture",
          streamId: streamId, // Chrome's stream ID for getUserMedia
          uniqueStreamId: uniqueStreamId, // Our unique ID for tracking
          tabId: tabId,
        });
      } catch (messageError) {
        // Clean up if offscreen message fails
        this.activeCaptures.delete(tabId);
        console.error(
          "Failed to communicate with offscreen document:",
          messageError
        );
        throw new Error(
          "Failed to initialize capture. Please try reloading the extension."
        );
      }

      return captureInfo;
    } catch (error) {
      console.error("Tab capture failed:", error);
      // Ensure cleanup on any failure
      this.activeCaptures.delete(tabId);
      throw error;
    }
  }

  async stopCapture(tabId) {
    const captureInfo = this.activeCaptures.get(tabId);
    if (!captureInfo) {
      console.log("No active capture found for tab:", tabId);
      return;
    }

    try {
      // Send message to offscreen document to stop capture
      await chrome.runtime.sendMessage({
        action: "stopTabCapture",
        streamId: captureInfo.streamId,
      });

      // Remove from active captures and streams
      this.activeCaptures.delete(tabId);

      // Clean up activeStreams for this tab
      for (const [streamId, streamInfo] of this.activeStreams.entries()) {
        if (streamInfo.tabId === tabId) {
          this.activeStreams.delete(streamId);
          console.log("Cleaned up stream:", streamId);
        }
      }

      console.log("Capture stopped for tab:", tabId);
    } catch (error) {
      console.error("Error stopping capture:", error);
      // Still remove from active captures even if offscreen message fails
      this.activeCaptures.delete(tabId);
    }
  }

  getCaptureStatus(tabId) {
    const captureInfo = this.activeCaptures.get(tabId);
    if (!captureInfo) {
      return {
        isCapturing: false,
        tabId: tabId,
      };
    }

    return {
      isCapturing: captureInfo.isActive,
      tabId: tabId,
      streamId: captureInfo.streamId,
      startTime: captureInfo.startTime,
      duration: Date.now() - captureInfo.startTime,
      audioTracks: captureInfo.audioTracks,
      videoTracks: captureInfo.videoTracks,
      method: captureInfo.method,
    };
  }

  async startWebRTCStream(tabId) {
    const captureInfo = this.activeCaptures.get(tabId);
    if (!captureInfo || !captureInfo.isActive) {
      throw new Error("No active capture found for tab. Start capture first.");
    }

    try {
      // Create peer connection in offscreen document
      const response = await chrome.runtime.sendMessage({
        action: "createPeerConnection",
        streamId: captureInfo.streamId,
        uniqueStreamId: captureInfo.uniqueStreamId,
        tabId: tabId,
      });

      if (!response.success) {
        throw new Error(response.error || "Failed to create peer connection");
      }

      // Update stream info with WebRTC details
      const streamInfo = this.activeStreams.get(captureInfo.uniqueStreamId);
      if (streamInfo) {
        streamInfo.webrtcInfo = {
          started: true,
          timestamp: Date.now(),
        };
      }

      console.log(
        `WebRTC stream started for tab ${tabId} (stream: ${captureInfo.uniqueStreamId})`
      );

      return {
        tabId: tabId,
        streamId: captureInfo.streamId,
        uniqueStreamId: captureInfo.uniqueStreamId,
        webrtcReady: true,
      };
    } catch (error) {
      console.error("Failed to start WebRTC stream:", error);
      throw error;
    }
  }

  async stopWebRTCStream(tabId) {
    try {
      // Stop WebRTC in offscreen document
      await chrome.runtime.sendMessage({
        action: "cleanup",
      });
      console.log(`WebRTC stream stopped for tab ${tabId}`);
    } catch (error) {
      console.error("Error stopping WebRTC stream:", error);
    }
  }

  async cleanup() {
    // Stop all active captures
    for (const [tabId] of this.activeCaptures) {
      await this.stopCapture(tabId);
    }

    // Clean up WebRTC connections in offscreen document
    try {
      await chrome.runtime.sendMessage({
        action: "cleanup",
      });
    } catch (error) {
      console.error("Error cleaning up WebRTC:", error);
    }

    // Close offscreen document if it exists
    if (this.offscreenDocumentCreated) {
      try {
        await chrome.offscreen.closeDocument();
        this.offscreenDocumentCreated = false;
        console.log("Offscreen document closed");
      } catch (error) {
        console.error("Error closing offscreen document:", error);
      }
    }
  }
}

// Initialize the extension
const tabScreenShareExtension = new TabScreenShareExtension();

// Cleanup on extension unload
chrome.runtime.onSuspend.addListener(() => {
  tabScreenShareExtension.cleanup();
});
